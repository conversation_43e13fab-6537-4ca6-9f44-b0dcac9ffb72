# Default values for egress.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: livekit/egress
  pullPolicy: IfNotPresent

egress:
  log_level: info
  health_port: 8080
  prometheus_port: 9090

terminationGracePeriodSeconds: 3600

nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false

nodeSelector: {}

resources: {}

serviceAccount:
  create: false
  annotations: {}
  name: ""

podAnnotations:
  sidecar.istio.io/inject: "false"
  linkerd.io/inject: disabled

podSecurityContext: {}

securityContext: {}

tolerations: []

affinity: {}

externalSecret:
  enabled: false
  name: ""
  refreshInterval: "30s"
  clusterSecretStore: ""
  gcpSecretManagerSecretsName: []

## @param extraEnvVarsSecret Name of existing Secret containing extra env vars
extraEnvVarsSecret: ""
