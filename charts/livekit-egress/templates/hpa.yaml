{{- if .Values.autoscaling.enabled }}
{{- if semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: autoscaling/v2
{{- else }}
apiVersion: autoscaling/v2beta1
{{- end }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "egress.fullname" . }}
  labels:
    {{- include "egress.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "egress.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        {{- if semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
        {{- else }}
        targetAverageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
        {{- end }}
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        {{- if semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
        {{- else }}
        targetAverageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
        {{- end }}
    {{- end }}
    {{- if .Values.autoscaling.custom }}
    - type: Pods
      pods:
        {{- if semverCompare ">=1.23-0" .Capabilities.KubeVersion.GitVersion }}
        metric:
          name: {{ .Values.autoscaling.custom.metricName }}
        target:
          type: AverageValue
          averageValue: {{ .Values.autoscaling.custom.targetAverageValue }}
        {{- else }}
        metricName: {{ .Values.autoscaling.custom.metricName }}
        targetAverageValue: {{ .Values.autoscaling.custom.targetAverageValue }}
        {{- end }}
    {{- end }}
{{- end }}
