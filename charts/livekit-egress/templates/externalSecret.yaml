{{- if .Values.externalSecret.enabled -}}
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ .Values.externalSecret.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "egress.labels" . | indent 4 }}
spec:
  refreshInterval: {{ .Values.externalSecret.refreshInterval }}  # Rate SecretManager pulls GCPSM
  secretStoreRef:
    kind: ClusterSecretStore
    name: {{ .Values.externalSecret.clusterSecretStore }}
  target:
    name: {{ .Values.externalSecret.name }}     # name of the k8s Secret to be created
    creationPolicy: Owner
  dataFrom:
{{- range .Values.externalSecret.gcpSecretManagerSecretsName }}
    - extract:
        key: {{ . | quote }}  # name of the GCPSM secret
{{- end }}
{{- end }}
