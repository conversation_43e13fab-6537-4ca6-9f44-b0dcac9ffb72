apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "egress.fullname" . }}
  labels:
    {{- include "egress.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "egress.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "egress.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "egress.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: EGRESS_CONFIG_BODY
              valueFrom:
                configMapKeyRef:
                  name: {{ include "egress.fullname" . }}
                  key: config.yaml
          {{- if .Values.extraEnvVarsSecret }}
          envFrom:
          - secretRef:
              name: {{ .Values.extraEnvVarsSecret }}
          {{- end }}
          ports:
            {{- if .Values.egress.health_port }}
            - name: health
              containerPort: {{ .Values.egress.health_port }}
              protocol: TCP
            {{- end }}
            {{- if .Values.egress.prometheus_port }}
            - name: metrics
              containerPort: {{ .Values.egress.prometheus_port }}
              protocol: TCP
            {{- end }}
          livenessProbe:
            httpGet:
              path: /
              port: health
          readinessProbe:
            httpGet:
              path: /
              port: health
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
