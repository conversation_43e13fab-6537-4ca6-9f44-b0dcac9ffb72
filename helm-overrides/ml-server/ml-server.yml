fullnameOverride: ml-server

image:
  repository: europe-docker.pkg.dev/bodyscratch-prod-eu-acd2/eu-docker/ml-server
  tag: 2.4-dev.26f77
  pullPolicy: IfNotPresent

configs:
  data:
    LIVEKIT_SERVER_URL: "http://livekit-server.livekit.svc.cluster.local"  # K8S local service
    KEYCLOAK_SERVER_URL: "http://keycloak.keycloak.svc.cluster.local"  # K8S local service
    KEYCLOAK_REALM: "bodyscratch"
    KEYCLOAK_CALLBACK_URI: "http://localhost:15000/callback"
    CHECKPOINT_PATH: "/workspace/checkpoints"
    CONFIG_CLASSIFICATION_THRESHOLD: "0.9"
    CONFIG_SLEEP_TIME_BETWEEN_FRAME: "2,1,18,2,18,1,2,2,0,14"
    CONFIG_NB_CONSECUTIVE_DETECTED_FRAME_TUTORIAL: "0.5"
    CONFIG_NB_CONSECUTIVE_DETECTED_FRAME_PRACTICE: "8"
    CONFIG_TIME_WAIT_FOR_COUNTING: "10"
    CONFIG_COUNT_FINGER_NB_KEYPOINT: "18"
    RABBITMQ_HOST: rabbitmq.rabbitmq.svc.cluster.local
    RABBITMQ_PORT: 5672
    RABBITMQ_QUEUE_NAME: ml-process
    RABBITMQ_EXCHANGE_NAME: ML
    RABBITMQ_EXCHANGE_TYPE: topic

externalSecret:
  enabled: true
  refreshInterval: 30s
  clusterSecretStore: "bodyscratch-prod-eu-gcp-store"
  gcpSecretManagerSecretsName:
    - "ml-server-secrets"

scaledJob:
  pollingInterval: 10             # Optional. Default: 30 seconds
  minReplicaCount: 0              # Optional. Default: 0
  maxReplicaCount: 10             # Optional. Default: 100
  successfulJobsHistoryLimit: 3   # Optional. Default: 100. How many completed jobs should be kept.
  failedJobsHistoryLimit: 2       # Optional. Default: 100. How many failed jobs should be kept.
  activeDeadlineSeconds: 3600     # Job will be terminated if it runs longer than this

  triggers:
    type: rabbitmq
    metadata:
      host: http://rabbitmq.rabbitmq.svc.cluster.local:15672
      queueName: ml-process
      mode: QueueLength
      value: "1"
      vhostName: "/"
      excludeUnacknowledged: "false"
    authenticationRef:
      name: keda-rabbitmq-cluster-trigger-authentication
      kind: ClusterTriggerAuthentication

  containers:
    resources:
      requests:
        cpu: 1
        memory: "1000Mi"

tolerations:
  - effect: NoSchedule
    key: "apps"
    operator: "Equal"
    value: "ml-server"

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: "apps"
          operator: "In"
          values:
          - "ml-server"