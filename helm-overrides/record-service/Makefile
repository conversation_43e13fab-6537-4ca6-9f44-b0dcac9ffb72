export HELMWAVE_LOG_LEVEL=info
export SERVICE_NAME=record-service
export NAMESPACE=apis

.build:
	@helmwave build --yml

validate: .build
	@helmwave validate
	@make clean

deploy: .build
	@helmwave up
	@helmwave ls
	@make clean

destroy: .build
	@helmwave down
	@make clean

rollback: .build
	@helmwave list
	@make clean

list: .build
	@helmwave list
	@make clean

clean:
	-rm -rf .helmwave
	-rm -rf helmwave.yml

.PHONY: clean list deploy validate destroy