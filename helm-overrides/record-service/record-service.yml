fullnameOverride: record-service

image:
  repository: europe-docker.pkg.dev/bodyscratch-prod-eu-acd2/eu-docker/record-service
  tag: 2.4-dev.0fc9
  pullPolicy: IfNotPresent

configs:
  data:
    LIVEKIT_SERVER_URL: "http://livekit-server.livekit.svc.cluster.local"  # K8S local service
    RABBITMQ_HOST: rabbitmq.rabbitmq.svc.cluster.local
    RABBITMQ_PORT: 5672
    RABBITMQ_QUEUE_NAME: record-service
    RABBITMQ_EXCHANGE_NAME: RECORD_SERVICE
    RABBITMQ_EXCHANGE_TYPE: topic
    GCS_BUCKET_NAME: bs-label-studio
    MP_MIN_POSE_PRESENCE_CONFIDENCE: 0.8
    MP_MIN_TRACKING_CONFIDENCE: 0.8
    MP_MIN_POSE_DETECTION_CONFIDENCE: 0.8

externalSecret:
  enabled: true
  refreshInterval: 30s
  clusterSecretStore: "bodyscratch-prod-eu-gcp-store"
  gcpSecretManagerSecretsName:
    - "record-service-secrets"

scaledJob:
  pollingInterval: 10             # Optional. Default: 30 seconds
  minReplicaCount: 0              # Optional. Default: 0
  maxReplicaCount: 10             # Optional. Default: 100
  successfulJobsHistoryLimit: 3   # Optional. Default: 100. How many completed jobs should be kept.
  failedJobsHistoryLimit: 2       # Optional. Default: 100. How many failed jobs should be kept.
  activeDeadlineSeconds: 3600     # Job will be terminated if it runs longer than this

  triggers:
    type: rabbitmq
    metadata:
      host: http://rabbitmq.rabbitmq.svc.cluster.local:15672
      queueName: record-service
      mode: QueueLength
      value: "1"
      vhostName: "/"
      excludeUnacknowledged: "false"
    authenticationRef:
      name: keda-rabbitmq-cluster-trigger-authentication
      kind: ClusterTriggerAuthentication

  containers:
    resources:
      requests:
        cpu: 2
        memory: "2000Mi"

tolerations:
  - effect: NoSchedule
    key: "apps"
    operator: "Equal"
    value: "record-service"

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: "apps"
          operator: "In"
          values:
          - "record-service"

serviceAccount:
  create: true
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>