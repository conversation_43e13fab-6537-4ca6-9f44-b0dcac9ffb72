replicaCount: 1

# Refer to https://docs.livekit.io/deploy/kubernetes/ for instructions

livekit:
  rtc:
    use_external_ip: true
  redis:
    use_tls: false
  turn:
    enabled: true
    domain: turn.live.bodyscratch.academy
    tls_port: 3478
    secretName: turn-livekit-tls-cert
    loadBalancerIP: ************  # livekit-turn-europe-west4-external-ip

certificate:
  # cert-manager will automatically issue a certificate for the domain(s) specified here
  secretName: turn-livekit-tls-cert  # K8S secret name
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - turn.live.bodyscratch.academy

loadBalancer:
  type: gke-pre-shared-cert
  staticIpName: "livekit-global-external-ip"
  certificateName: "livekit-ssl-cert"
  tls:
    - hosts:
        - live.bodyscratch.academy

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 60

# resources are set assuming a 8 core instance
resources:
  requests:
    cpu: 100m
    memory: 200Mi
  limits:
    cpu: 1500m
    memory: 1000Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: apps
              operator: In
              values:
                - livekit

tolerations:
  - key: "apps"
    operator: "Equal"
    value: "livekit"
    effect: "NoSchedule"

externalSecret:
  enabled: true
  name: livekit-server-secrets
  refreshInterval: 30s
  clusterSecretStore: "bodyscratch-prod-eu-gcp-store"
  gcpSecretManagerSecretsName:
    - "livekit-secrets"

## @param extraEnvVarsSecret Name of existing Secret containing extra env vars
extraEnvVarsSecret: "livekit-server-secrets"

updateStrategy:
  type: Recreate
  # rollingUpdate:
  #   maxSurge: 1
  #   maxUnavailable: 0