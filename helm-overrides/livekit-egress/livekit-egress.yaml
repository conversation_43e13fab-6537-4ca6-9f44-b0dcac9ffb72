replicaCount: 1

egress:
  enable_chrome_sandbox: true
  redis:
    use_tls: false
  gcp:
    bucket: bs-label-studio

serviceAccount:
  create: true
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 60

resources:
  requests:
    cpu: 100m
    memory: 200Mi
  limits:
    cpu: 1500m
    memory: 1000Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: "apps"
              operator: "In"
              values:
                - "livekit-egress"

tolerations:
  - key: "apps"
    operator: "Equal"
    value: "livekit-egress"
    effect: "NoSchedule"

externalSecret:
  enabled: true
  name: livekit-egress-secrets
  refreshInterval: 30s
  clusterSecretStore: "bodyscratch-prod-eu-gcp-store"
  gcpSecretManagerSecretsName:
    - "livekit-egress-secrets"

## @param extraEnvVarsSecret Name of existing Secret containing extra env vars
extraEnvVarsSecret: "livekit-egress-secrets"

updateStrategy:
  type: Recreate
  # rollingUpdate:
  #   maxSurge: 1
  #   maxUnavailable: 0