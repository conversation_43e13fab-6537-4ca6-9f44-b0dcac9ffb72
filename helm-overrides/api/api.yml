fullnameOverride: api
env: prod

dataDog:
  enabled: false

image:
  repository: europe-docker.pkg.dev/bodyscratch-prod-eu-acd2/eu-docker/api
  tag: 2.4-dev.7d63
  pullPolicy: Always

deployment:
  replicaCount: 2

  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1

  container:
    resources:
      requests:
        cpu: "100m"
        memory: "100Mi"
      limits:
        cpu: "2"
        memory: "1Gi"

externalSecret:
  enabled: true
  name: api-secrets
  refreshInterval: 30s
  clusterSecretStore: "bodyscratch-prod-eu-gcp-store"
  gcpSecretManagerSecretsName:
    - "api-secrets"

configs:
  data:
    QUARKUS_OIDC_AUTH_SERVER_URL: http://keycloak.keycloak.svc.cluster.local/realms/bodyscratch
    LIVEKIT_SERVER_URL: http://livekit-server.livekit.svc.cluster.local
    KEYCLOAK_SERVER_URL: http://keycloak.keycloak.svc.cluster.local
    PUBLIC_MEDIA_DOMAIN: media.bodyscratch.academy
    MLPROCESS_QUEUE_ID: ml-process
    STORAGE_BUCKET_NAME: bodyscratch-media-website
    MP_MESSAGING_OUTGOING_MLPROCESS_CONNECTOR: smallrye-rabbitmq
    MP_MESSAGING_OUTGOING_MLPROCESS_EXCHANGE_NAME: ML
    MP_MESSAGING_OUTGOING_MLPROCESS_EXCHANGE_TYPE: topic
    MP_MESSAGING_OUTGOING_MLPROCESS_EXCHANGE_DECLARE: false
    RECORDSERVICE_QUEUE_ID: record-service
    MP_MESSAGING_OUTGOING_RECORDSERVICE_CONNECTOR: smallrye-rabbitmq
    MP_MESSAGING_OUTGOING_RECORDSERVICE_EXCHANGE_NAME: RECORD_SERVICE
    MP_MESSAGING_OUTGOING_RECORDSERVICE_EXCHANGE_TYPE: topic
    MP_MESSAGING_OUTGOING_RECORDSERVICE_EXCHANGE_DECLARE: false
    RABBITMQ_HOST: rabbitmq.rabbitmq.svc.cluster.local
    RABBITMQ_PORT: 5672

service:
  type: ClusterIP
  port:
    http: 80
  targetPort:
    http: 8080

ingress:
  enabled: true
  ingressClassName: gce
  annotations:
    kubernetes.io/ingress.class: gce
    kubernetes.io/ingress.global-static-ip-name: "api-global-external-ip"
    ingress.gcp.kubernetes.io/pre-shared-cert: "api-ssl-cert"
  rules:
    - host: "api.bodyscratch.academy"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api
                port:
                  name: http

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /q/health/live
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 10
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 5

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /q/health/ready
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 15
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 5

tolerations:
  - effect: NoSchedule
    key: "apps"
    operator: "Equal"
    value: "backend"

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: "apps"
          operator: "In"
          values:
          - "backend"

serviceAccount:
  create: true
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>